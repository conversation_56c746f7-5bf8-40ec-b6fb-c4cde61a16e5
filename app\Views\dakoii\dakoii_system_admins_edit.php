<?= $this->extend("templates/dakoii_template"); ?>
<?= $this->section('content'); ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/system-admins') ?>">System Administrators</a></li>
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/system-admins/show/' . $admin['id']) ?>"><?= esc($admin['name']) ?></a></li>
        <li class="breadcrumb-item active">Edit</li>
    </ol>
</nav>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-0">Edit System Administrator</h2>
        <p class="text-muted mb-0">Update administrator information for <?= esc($admin['name']) ?></p>
    </div>
    <div class="btn-group">
        <a href="<?= base_url('dakoii/system-admins/show/' . $admin['id']) ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Details
        </a>
        <a href="<?= base_url('dakoii/system-admins') ?>" class="btn btn-outline-secondary">
            <i class="fas fa-list"></i> Back to List
        </a>
    </div>
</div>

<!-- Flash Messages -->
<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle"></i> <?= session()->getFlashdata('error') ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Edit Form -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-edit"></i> Administrator Information
                </h5>
            </div>
            <div class="card-body">
                <?= form_open('dakoii/system-admins/update/' . $admin['id']) ?>
                <?= csrf_field() ?>

                <div class="row">
                    <!-- Organization Selection -->
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Organization <span class="text-danger">*</span></label>
                        <select class="form-select" name="orgcode" required>
                            <option value="">Select Organization</option>
                            <?php foreach ($organizations as $org): ?>
                                <option value="<?= esc($org['orgcode']) ?>"
                                        <?= ((isset($admin['orgcode']) && $admin['orgcode'] == $org['orgcode']) || old('orgcode') == $org['orgcode']) ? 'selected' : '' ?>>
                                    <?= esc($org['name']) ?> (<?= esc($org['orgcode']) ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Position -->
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Position</label>
                        <input type="text" class="form-control" name="position"
                               placeholder="Job title or position"
                               value="<?= old('position', $admin['position']) ?>">
                        <div class="form-text">Optional job title or position</div>
                    </div>
                </div>

                <div class="row">
                    <!-- Full Name -->
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Full Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="name" required
                               placeholder="Enter full name"
                               value="<?= old('name', $admin['name']) ?>">
                    </div>

                    <!-- Email -->
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Email <span class="text-danger">*</span></label>
                        <input type="email" class="form-control" name="email" required
                               placeholder="Enter email address"
                               value="<?= old('email', $admin['email']) ?>">
                        <div class="form-text">Must be unique across the system</div>
                    </div>
                </div>

                <div class="row">
                    <!-- Password -->
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Password</label>
                        <input type="password" class="form-control" name="password"
                               placeholder="Leave blank to keep current password" minlength="6">
                        <div class="form-text">Leave blank to keep current password. Minimum 6 characters if changing.</div>
                    </div>


                </div>

                <div class="row">
                    <!-- Email -->
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Email Address</label>
                        <input type="email" class="form-control" name="email"
                               placeholder="Enter email address"
                               value="<?= old('email', $admin['email']) ?>">
                    </div>

                    <!-- Phone -->
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Phone Number</label>
                        <input type="text" class="form-control" name="phone"
                               placeholder="Enter phone number"
                               value="<?= old('phone', $admin['phone']) ?>">
                    </div>
                </div>

                <!-- Status -->
                <div class="mb-3">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="status" name="status" value="1"
                               <?= (old('status', $admin['status'])) ? 'checked' : '' ?>>
                        <label class="form-check-label" for="status">
                            <strong>Active Account</strong>
                        </label>
                        <div class="form-text">Uncheck to deactivate the account</div>
                    </div>
                </div>

                <div class="d-flex justify-content-between">
                    <a href="<?= base_url('dakoii/system-admins/show/' . $admin['id']) ?>" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Update Administrator
                    </button>
                </div>

                <?= form_close() ?>
            </div>
        </div>
    </div>

    <!-- Current Info & Help Panel -->
    <div class="col-lg-4">
        <!-- Current Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i> Current Information
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="fw-medium text-muted">Current Status</div>
                    <span class="badge bg-<?= $admin['status'] ? 'success' : 'danger' ?>">
                        <?= $admin['status'] ? 'Active' : 'Inactive' ?>
                    </span>
                </div>

                <div class="mb-3">
                    <div class="fw-medium text-muted">Created</div>
                    <div><?= date('M j, Y', strtotime($admin['created_at'])) ?></div>
                </div>

                <div class="mb-3">
                    <div class="fw-medium text-muted">Last Updated</div>
                    <div><?= date('M j, Y', strtotime($admin['updated_at'])) ?></div>
                </div>

                <?php if (!empty($admin['updated_by'])): ?>
                <div class="mb-3">
                    <div class="fw-medium text-muted">Last Updated By</div>
                    <div><?= esc($admin['updated_by']) ?></div>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Help Panel -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-question-circle"></i> Help
                </h6>
            </div>
            <div class="card-body">
                <h6>Password Update</h6>
                <p class="small text-muted">
                    Leave the password field blank to keep the current password.
                    Only enter a new password if you want to change it.
                </p>

                <h6>Email Changes</h6>
                <p class="small text-muted">
                    Changing the email will affect the administrator's login credentials.
                    Make sure to inform them of any changes.
                </p>

                <h6>Account Status</h6>
                <p class="small text-muted">
                    Deactivating an account will prevent the administrator from logging in,
                    but their data will be preserved.
                </p>

                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Note:</strong> Changes will take effect immediately after saving.
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
