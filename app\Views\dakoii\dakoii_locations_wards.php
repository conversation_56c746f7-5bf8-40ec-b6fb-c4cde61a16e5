<?= $this->extend('templates/dakoii_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-map"></i> Wards - <?= esc($llg['name']) ?>
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/locations') ?>">Locations</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/locations/provinces') ?>">Provinces</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/locations/districts/' . $province['id']) ?>"><?= esc($province['name']) ?> Districts</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('dakoii/locations/llgs/' . $district['id']) ?>"><?= esc($district['name']) ?> LLGs</a></li>
                    <li class="breadcrumb-item active"><?= esc($llg['name']) ?> Wards</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="<?= base_url('dakoii/locations/wards/create/' . $llg['id']) ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add New Ward
            </a>
            <a href="<?= base_url('dakoii/locations/llgs/' . $district['id']) ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to LLGs
            </a>
        </div>
    </div>

    <!-- LLG Info Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-success">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h5 class="card-title text-success">
                                <i class="fas fa-landmark"></i> <?= esc($llg['name']) ?>
                            </h5>
                            <p class="card-text">
                                <span class="badge bg-secondary me-2">Code: <?= esc($llg['llgcode']) ?></span>
                                <span class="badge bg-info me-2">JSON ID: <?= esc($llg['json_id']) ?></span>
                                <span class="badge bg-primary me-2">District: <?= esc($district['name']) ?></span>
                                <span class="badge bg-warning me-2">Province: <?= esc($province['name']) ?></span>
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="text-center">
                                <h4 class="text-success"><?= count($wards) ?></h4>
                                <small class="text-muted">Total Wards</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i> <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i> <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Wards Table -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list"></i> Wards List
                </h5>
                <div class="d-flex gap-2">
                    <input type="text" class="form-control form-control-sm" id="searchInput" 
                           placeholder="Search wards..." style="width: 250px;">
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="wardsTable">
                    <thead class="table-light">
                        <tr>
                            <th>#</th>
                            <th>Ward Name</th>
                            <th>Ward Code</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($wards)): ?>
                            <?php $i = 1; foreach ($wards as $ward): ?>
                            <tr>
                                <td><?= $i++ ?></td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-map text-success me-2"></i>
                                        <strong><?= esc($ward['name']) ?></strong>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary"><?= esc($ward['wardcode']) ?></span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= base_url('dakoii/locations/wards/edit/' . $ward['id']) ?>" 
                                           class="btn btn-sm btn-outline-primary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="confirmDelete(<?= $ward['id'] ?>, '<?= esc($ward['name']) ?>')" 
                                                title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="4" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-map fa-3x mb-3"></i>
                                        <p>No wards found in <?= esc($llg['name']) ?></p>
                                        <a href="<?= base_url('dakoii/locations/wards/create/' . $llg['id']) ?>" class="btn btn-primary">
                                            <i class="fas fa-plus"></i> Add First Ward
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
        <?php if (!empty($wards)): ?>
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <small class="text-muted">
                    Showing <span id="showingCount"><?= count($wards) ?></span> of <?= count($wards) ?> wards
                </small>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the ward <strong id="wardName"></strong>?</p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const tbody = document.querySelector('#wardsTable tbody');
    const showingCount = document.getElementById('showingCount');

    // Search functionality
    if (searchInput) {
        searchInput.addEventListener('input', filterTable);
    }

    function filterTable() {
        const searchTerm = searchInput.value.toLowerCase();
        const rows = tbody.querySelectorAll('tr');
        let visibleCount = 0;

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });

        if (showingCount) {
            showingCount.textContent = visibleCount;
        }
    }
});

function confirmDelete(id, name) {
    document.getElementById('wardName').textContent = name;
    document.getElementById('deleteForm').action = '<?= base_url('dakoii/locations/wards/') ?>' + id + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>

<?= $this->endSection() ?>
