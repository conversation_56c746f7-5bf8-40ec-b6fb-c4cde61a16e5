<?php

namespace App\Controllers;

use App\Models\AdxCountryModel;
use App\Models\AdxProvinceModel;
use App\Models\AdxDistrictModel;
use App\Models\AdxLlgModel;
use App\Models\AdxWardModel;
use App\Models\GovStructureModel;

class DakoiiLocations extends BaseController
{
    public $session;
    public $countryModel;
    public $provinceModel;
    public $districtModel;
    public $llgModel;
    public $wardModel;
    public $govStructureModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        
        // Initialize models
        $this->countryModel = new AdxCountryModel();
        $this->provinceModel = new AdxProvinceModel();
        $this->districtModel = new AdxDistrictModel();
        $this->llgModel = new AdxLlgModel();
        $this->wardModel = new AdxWardModel();
        $this->govStructureModel = new GovStructureModel();
    }

    /**
     * Display locations overview
     */
    public function index()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Locations Management";
        $data['menu'] = "locations";
        
        // Get location statistics
        $data['countries'] = $this->countryModel->findAll();
        $data['provinces'] = $this->provinceModel->findAll();
        $data['provinces_count'] = $this->provinceModel->countAllResults();
        $data['districts_count'] = $this->districtModel->countAllResults();
        $data['llgs_count'] = $this->llgModel->countAllResults();
        $data['wards_count'] = $this->wardModel->countAllResults();

        // Get province statistics
        $data['province_stats'] = $this->getProvinceStats();

        return view('dakoii/dakoii_locations_index', $data);
    }

    /**
     * Display provinces
     */
    public function provinces()
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Provinces";
        $data['menu'] = "locations";
        $data['provinces'] = $this->provinceModel->findAll();
        $data['countries'] = $this->countryModel->findAll();

        // Get statistics for each province
        foreach ($data['provinces'] as &$province) {
            $province['districts_count'] = $this->districtModel->where('province_id', $province['id'])->countAllResults();
            $province['llgs_count'] = $this->llgModel->where('province_id', $province['id'])->countAllResults();
            $province['wards_count'] = $this->wardModel->where('province_id', $province['id'])->countAllResults();
        }

        return view('dakoii/dakoii_locations_provinces', $data);
    }

    /**
     * Display districts for a province
     */
    public function districts($provinceId)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $province = $this->provinceModel->find($provinceId);
        if (!$province) {
            session()->setFlashdata('error', 'Province not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        $data['title'] = "Districts - " . $province['name'];
        $data['menu'] = "locations";
        $data['province'] = $province;
        $data['districts'] = $this->districtModel->where('province_id', $provinceId)->findAll();

        // Get statistics for each district
        foreach ($data['districts'] as &$district) {
            $district['llgs_count'] = $this->llgModel->where('district_id', $district['id'])->countAllResults();
            $district['wards_count'] = $this->wardModel->where('district_id', $district['id'])->countAllResults();
        }

        return view('dakoii/dakoii_locations_districts', $data);
    }

    /**
     * Display LLGs for a district
     */
    public function llgs($districtId)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $district = $this->districtModel->find($districtId);
        if (!$district) {
            session()->setFlashdata('error', 'District not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        $province = $this->provinceModel->find($district['province_id']);

        $data['title'] = "LLGs - " . $district['name'];
        $data['menu'] = "locations";
        $data['district'] = $district;
        $data['province'] = $province;
        $data['llgs'] = $this->llgModel->where('district_id', $districtId)->findAll();

        // Get statistics for each LLG
        foreach ($data['llgs'] as &$llg) {
            $llg['wards_count'] = $this->wardModel->where('llg_id', $llg['id'])->countAllResults();
        }

        return view('dakoii/llgs', $data);
    }

    /**
     * Display wards for an LLG
     */
    public function wards($llgId)
    {
        // Check authentication
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $llg = $this->llgModel->find($llgId);
        if (!$llg) {
            session()->setFlashdata('error', 'LLG not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        $district = $this->districtModel->find($llg['district_id']);
        $province = $this->provinceModel->find($llg['province_id']);

        $data['title'] = "Wards - " . $llg['name'];
        $data['menu'] = "locations";
        $data['llg'] = $llg;
        $data['district'] = $district;
        $data['province'] = $province;
        $data['wards'] = $this->wardModel->where('llg_id', $llgId)->findAll();

        return view('dakoii/wards', $data);
    }

    // ========== WARD CRUD METHODS ==========

    /**
     * Show create ward form
     */
    public function createWard($llgId = null)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Add New Ward";
        $data['menu'] = "locations";
        $data['countries'] = $this->countryModel->orderBy('name', 'ASC')->findAll();
        $data['provinces'] = $this->provinceModel->orderBy('name', 'ASC')->findAll();
        $data['districts'] = $this->districtModel->orderBy('name', 'ASC')->findAll();
        $data['llgs'] = $this->llgModel->orderBy('name', 'ASC')->findAll();
        $data['selected_llg_id'] = $llgId;

        return view('dakoii/wards_create', $data);
    }

    /**
     * Store new ward
     */
    public function storeWard()
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $rules = [
            'name' => 'required|min_length[2]|max_length[100]',
            'wardcode' => 'required|min_length[2]|max_length[10]|is_unique[wards.wardcode]',
            'llg_id' => 'required|integer',
            'district_id' => 'required|integer',
            'province_id' => 'required|integer',
            'country_id' => 'required|integer'
        ];

        if (!$this->validate($rules)) {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
            return redirect()->back()->withInput();
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'wardcode' => strtoupper($this->request->getPost('wardcode')),
            'country_id' => $this->request->getPost('country_id'),
            'province_id' => $this->request->getPost('province_id'),
            'district_id' => $this->request->getPost('district_id'),
            'llg_id' => $this->request->getPost('llg_id'),
            'created_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->wardModel->insert($data)) {
            session()->setFlashdata('success', 'Ward added successfully');
            $llgId = $this->request->getPost('llg_id');
            return redirect()->to("dakoii/locations/wards/{$llgId}");
        } else {
            session()->setFlashdata('error', 'Failed to add ward');
            return redirect()->back()->withInput();
        }
    }

    /**
     * Show edit ward form
     */
    public function editWard($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $ward = $this->wardModel->find($id);
        if (!$ward) {
            session()->setFlashdata('error', 'Ward not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        $data['title'] = "Edit Ward - " . $ward['name'];
        $data['menu'] = "locations";
        $data['ward'] = $ward;
        $data['llg'] = $this->llgModel->find($ward['llg_id']);
        $data['district'] = $this->districtModel->find($ward['district_id']);
        $data['province'] = $this->provinceModel->find($ward['province_id']);
        $data['countries'] = $this->countryModel->orderBy('name', 'ASC')->findAll();
        $data['provinces'] = $this->provinceModel->orderBy('name', 'ASC')->findAll();
        $data['districts'] = $this->districtModel->orderBy('name', 'ASC')->findAll();
        $data['llgs'] = $this->llgModel->orderBy('name', 'ASC')->findAll();

        return view('dakoii/wards_edit', $data);
    }

    /**
     * Update ward
     */
    public function updateWard($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $ward = $this->wardModel->find($id);
        if (!$ward) {
            session()->setFlashdata('error', 'Ward not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        $rules = [
            'name' => 'required|min_length[2]|max_length[100]',
            'wardcode' => "required|min_length[2]|max_length[10]|is_unique[wards.wardcode,id,{$id}]",
            'llg_id' => 'required|integer',
            'district_id' => 'required|integer',
            'province_id' => 'required|integer',
            'country_id' => 'required|integer'
        ];

        if (!$this->validate($rules)) {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
            return redirect()->back()->withInput();
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'wardcode' => strtoupper($this->request->getPost('wardcode')),
            'country_id' => $this->request->getPost('country_id'),
            'province_id' => $this->request->getPost('province_id'),
            'district_id' => $this->request->getPost('district_id'),
            'llg_id' => $this->request->getPost('llg_id'),
            'updated_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->wardModel->update($id, $data)) {
            session()->setFlashdata('success', 'Ward updated successfully');
            $llgId = $this->request->getPost('llg_id');
            return redirect()->to("dakoii/locations/wards/{$llgId}");
        } else {
            session()->setFlashdata('error', 'Failed to update ward');
            return redirect()->back()->withInput();
        }
    }

    /**
     * Delete ward
     */
    public function deleteWard($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $ward = $this->wardModel->find($id);
        if (!$ward) {
            session()->setFlashdata('error', 'Ward not found');
            return redirect()->back();
        }

        if ($this->wardModel->delete($id)) {
            session()->setFlashdata('success', 'Ward deleted successfully');
        } else {
            session()->setFlashdata('error', 'Failed to delete ward');
        }

        return redirect()->to("dakoii/locations/wards/{$ward['llg_id']}");
    }

    /**
     * Get province statistics
     */
    private function getProvinceStats()
    {
        $provinces = $this->provinceModel->findAll();
        $stats = [];

        foreach ($provinces as $province) {
            $districts = $this->districtModel->where('province_id', $province['id'])->countAllResults();
            $llgs = $this->llgModel->where('province_id', $province['id'])->countAllResults();
            $wards = $this->wardModel->where('province_id', $province['id'])->countAllResults();

            $stats[] = [
                'province' => $province,
                'districts_count' => $districts,
                'llgs_count' => $llgs,
                'wards_count' => $wards,
                'total_locations' => $districts + $llgs + $wards
            ];
        }

        // Sort by total locations descending
        usort($stats, function($a, $b) {
            return $b['total_locations'] - $a['total_locations'];
        });

        return $stats;
    }

    /**
     * Check if user is authenticated for Dakoii portal
     */
    private function isAuthenticated(): bool
    {
        return $this->session->has('dakoii_logged_in') && $this->session->get('dakoii_logged_in') === true;
    }

    /**
     * Get current user information
     */
    private function getCurrentUser()
    {
        if (!$this->isAuthenticated()) {
            return null;
        }

        return [
            'id' => $this->session->get('dakoii_user_id'),
            'name' => $this->session->get('dakoii_name'),
            'username' => $this->session->get('dakoii_username'),
            'role' => $this->session->get('dakoii_role'),
            'orgcode' => $this->session->get('dakoii_orgcode')
        ];
    }

    // ========== COUNTRY CRUD METHODS ==========

    /**
     * Display countries list
     */
    public function countries()
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Countries Management";
        $data['menu'] = "locations";
        $data['countries'] = $this->countryModel->orderBy('name', 'ASC')->findAll();

        return view('dakoii/dakoii_locations_countries', $data);
    }

    /**
     * Show create country form
     */
    public function createCountry()
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Add New Country";
        $data['menu'] = "locations";

        return view('dakoii/dakoii_locations_country_create', $data);
    }

    /**
     * Store new country
     */
    public function storeCountry()
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $rules = [
            'name' => 'required|min_length[2]|max_length[255]',
            'code' => 'required|min_length[2]|max_length[10]|is_unique[adx_country.code]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'code' => strtoupper($this->request->getPost('code')),
            'created_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->countryModel->insert($data)) {
            session()->setFlashdata('success', 'Country added successfully');
        } else {
            session()->setFlashdata('error', 'Failed to add country');
        }

        return redirect()->to('dakoii/locations/countries');
    }

    /**
     * Show edit country form
     */
    public function editCountry($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $country = $this->countryModel->find($id);
        if (!$country) {
            session()->setFlashdata('error', 'Country not found');
            return redirect()->to('dakoii/locations/countries');
        }

        $data['title'] = "Edit Country - " . $country['name'];
        $data['menu'] = "locations";
        $data['country'] = $country;

        return view('dakoii/dakoii_locations_country_edit', $data);
    }

    /**
     * Update country
     */
    public function updateCountry($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $country = $this->countryModel->find($id);
        if (!$country) {
            session()->setFlashdata('error', 'Country not found');
            return redirect()->to('dakoii/locations/countries');
        }

        $rules = [
            'name' => 'required|min_length[2]|max_length[255]',
            'code' => "required|min_length[2]|max_length[10]|is_unique[adx_country.code,id,{$id}]"
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'code' => strtoupper($this->request->getPost('code')),
            'updated_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->countryModel->update($id, $data)) {
            session()->setFlashdata('success', 'Country updated successfully');
        } else {
            session()->setFlashdata('error', 'Failed to update country');
        }

        return redirect()->to('dakoii/locations/countries');
    }

    /**
     * Delete country
     */
    public function deleteCountry($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $country = $this->countryModel->find($id);
        if (!$country) {
            session()->setFlashdata('error', 'Country not found');
            return redirect()->to('dakoii/locations/countries');
        }

        // Check if country has provinces
        $provinceCount = $this->provinceModel->where('country_id', $id)->countAllResults();
        if ($provinceCount > 0) {
            session()->setFlashdata('error', 'Cannot delete country that has provinces');
            return redirect()->to('dakoii/locations/countries');
        }

        if ($this->countryModel->delete($id)) {
            session()->setFlashdata('success', 'Country deleted successfully');
        } else {
            session()->setFlashdata('error', 'Failed to delete country');
        }

        return redirect()->to('dakoii/locations/countries');
    }

    // ========== PROVINCE CRUD METHODS ==========

    /**
     * Show create province form
     */
    public function createProvince()
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Add New Province";
        $data['menu'] = "locations";
        $data['countries'] = $this->countryModel->orderBy('name', 'ASC')->findAll();

        return view('dakoii/provinces_create', $data);
    }

    /**
     * Store new province
     */
    public function storeProvince()
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $rules = [
            'name' => 'required|min_length[2]|max_length[255]',
            'provincecode' => 'required|min_length[2]|max_length[20]|is_unique[adx_province.provincecode]',
            'country_id' => 'required|integer',
            'json_id' => 'required|max_length[255]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'provincecode' => strtoupper($this->request->getPost('provincecode')),
            'country_id' => $this->request->getPost('country_id'),
            'json_id' => $this->request->getPost('json_id'),
            'created_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->provinceModel->insert($data)) {
            session()->setFlashdata('success', 'Province added successfully');
        } else {
            session()->setFlashdata('error', 'Failed to add province');
        }

        return redirect()->to('dakoii/locations/provinces');
    }

    /**
     * Show edit province form
     */
    public function editProvince($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $province = $this->provinceModel->find($id);
        if (!$province) {
            session()->setFlashdata('error', 'Province not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        $data['title'] = "Edit Province - " . $province['name'];
        $data['menu'] = "locations";
        $data['province'] = $province;
        $data['countries'] = $this->countryModel->orderBy('name', 'ASC')->findAll();

        return view('dakoii/provinces_edit', $data);
    }

    /**
     * Update province
     */
    public function updateProvince($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $province = $this->provinceModel->find($id);
        if (!$province) {
            session()->setFlashdata('error', 'Province not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        $rules = [
            'name' => 'required|min_length[2]|max_length[255]',
            'provincecode' => "required|min_length[2]|max_length[20]|is_unique[adx_province.provincecode,id,{$id}]",
            'country_id' => 'required|integer',
            'json_id' => 'required|max_length[255]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'provincecode' => strtoupper($this->request->getPost('provincecode')),
            'country_id' => $this->request->getPost('country_id'),
            'json_id' => $this->request->getPost('json_id'),
            'updated_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->provinceModel->update($id, $data)) {
            session()->setFlashdata('success', 'Province updated successfully');
        } else {
            session()->setFlashdata('error', 'Failed to update province');
        }

        return redirect()->to('dakoii/locations/provinces');
    }

    /**
     * Delete province
     */
    public function deleteProvince($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $province = $this->provinceModel->find($id);
        if (!$province) {
            session()->setFlashdata('error', 'Province not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        // Check if province has districts
        $districtCount = $this->districtModel->where('province_id', $id)->countAllResults();
        if ($districtCount > 0) {
            session()->setFlashdata('error', 'Cannot delete province that has districts');
            return redirect()->to('dakoii/locations/provinces');
        }

        if ($this->provinceModel->delete($id)) {
            session()->setFlashdata('success', 'Province deleted successfully');
        } else {
            session()->setFlashdata('error', 'Failed to delete province');
        }

        return redirect()->to('dakoii/locations/provinces');
    }

    // ========== DISTRICT CRUD METHODS ==========

    /**
     * Show create district form
     */
    public function createDistrict($provinceId = null)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Add New District";
        $data['menu'] = "locations";
        $data['countries'] = $this->countryModel->orderBy('name', 'ASC')->findAll();
        $data['provinces'] = $this->provinceModel->orderBy('name', 'ASC')->findAll();
        $data['selected_province_id'] = $provinceId;

        return view('dakoii/districts_create', $data);
    }

    /**
     * Store new district
     */
    public function storeDistrict()
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $rules = [
            'name' => 'required|min_length[2]|max_length[255]',
            'districtcode' => 'required|min_length[2]|max_length[20]|is_unique[adx_district.districtcode]',
            'country_id' => 'required|integer',
            'province_id' => 'required|integer',
            'json_id' => 'required|max_length[255]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'districtcode' => strtoupper($this->request->getPost('districtcode')),
            'country_id' => $this->request->getPost('country_id'),
            'province_id' => $this->request->getPost('province_id'),
            'json_id' => $this->request->getPost('json_id'),
            'created_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->districtModel->insert($data)) {
            session()->setFlashdata('success', 'District added successfully');
            $provinceId = $this->request->getPost('province_id');
            return redirect()->to("dakoii/locations/districts/{$provinceId}");
        } else {
            session()->setFlashdata('error', 'Failed to add district');
            return redirect()->back()->withInput();
        }
    }

    /**
     * Show edit district form
     */
    public function editDistrict($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $district = $this->districtModel->find($id);
        if (!$district) {
            session()->setFlashdata('error', 'District not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        $data['title'] = "Edit District - " . $district['name'];
        $data['menu'] = "locations";
        $data['district'] = $district;
        $data['countries'] = $this->countryModel->orderBy('name', 'ASC')->findAll();
        $data['provinces'] = $this->provinceModel->orderBy('name', 'ASC')->findAll();

        return view('dakoii/districts_edit', $data);
    }

    /**
     * Update district
     */
    public function updateDistrict($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $district = $this->districtModel->find($id);
        if (!$district) {
            session()->setFlashdata('error', 'District not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        $rules = [
            'name' => 'required|min_length[2]|max_length[255]',
            'districtcode' => "required|min_length[2]|max_length[20]|is_unique[adx_district.districtcode,id,{$id}]",
            'country_id' => 'required|integer',
            'province_id' => 'required|integer',
            'json_id' => 'required|max_length[255]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'districtcode' => strtoupper($this->request->getPost('districtcode')),
            'country_id' => $this->request->getPost('country_id'),
            'province_id' => $this->request->getPost('province_id'),
            'json_id' => $this->request->getPost('json_id'),
            'updated_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->districtModel->update($id, $data)) {
            session()->setFlashdata('success', 'District updated successfully');
            $provinceId = $this->request->getPost('province_id');
            return redirect()->to("dakoii/locations/districts/{$provinceId}");
        } else {
            session()->setFlashdata('error', 'Failed to update district');
            return redirect()->back()->withInput();
        }
    }

    /**
     * Delete district
     */
    public function deleteDistrict($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $district = $this->districtModel->find($id);
        if (!$district) {
            session()->setFlashdata('error', 'District not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        // Check if district has LLGs
        $llgCount = $this->llgModel->where('district_id', $id)->countAllResults();
        if ($llgCount > 0) {
            session()->setFlashdata('error', 'Cannot delete district that has LLGs');
            $provinceId = $district['province_id'];
            return redirect()->to("dakoii/locations/districts/{$provinceId}");
        }

        $provinceId = $district['province_id'];
        if ($this->districtModel->delete($id)) {
            session()->setFlashdata('success', 'District deleted successfully');
        } else {
            session()->setFlashdata('error', 'Failed to delete district');
        }

        return redirect()->to("dakoii/locations/districts/{$provinceId}");
    }

    // ========== LLG CRUD METHODS ==========

    /**
     * Show create LLG form
     */
    public function createLlg($districtId = null)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $data['title'] = "Add New LLG";
        $data['menu'] = "locations";
        $data['countries'] = $this->countryModel->orderBy('name', 'ASC')->findAll();
        $data['provinces'] = $this->provinceModel->orderBy('name', 'ASC')->findAll();
        $data['districts'] = $this->districtModel->orderBy('name', 'ASC')->findAll();
        $data['selected_district_id'] = $districtId;

        return view('dakoii/llgs_create', $data);
    }

    /**
     * Store new LLG
     */
    public function storeLlg()
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $rules = [
            'name' => 'required|min_length[2]|max_length[255]',
            'llgcode' => 'required|min_length[2]|max_length[20]|is_unique[adx_llg.llgcode]',
            'country_id' => 'required|integer',
            'province_id' => 'required|integer',
            'district_id' => 'required|integer'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'llgcode' => strtoupper($this->request->getPost('llgcode')),
            'country_id' => $this->request->getPost('country_id'),
            'province_id' => $this->request->getPost('province_id'),
            'district_id' => $this->request->getPost('district_id'),
            'created_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->llgModel->insert($data)) {
            session()->setFlashdata('success', 'LLG added successfully');
            $districtId = $this->request->getPost('district_id');
            return redirect()->to("dakoii/locations/llgs/{$districtId}");
        } else {
            session()->setFlashdata('error', 'Failed to add LLG');
            return redirect()->back()->withInput();
        }
    }

    /**
     * Show edit LLG form
     */
    public function editLlg($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $llg = $this->llgModel->find($id);
        if (!$llg) {
            session()->setFlashdata('error', 'LLG not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        $data['title'] = "Edit LLG - " . $llg['name'];
        $data['menu'] = "locations";
        $data['llg'] = $llg;
        $data['district'] = $this->districtModel->find($llg['district_id']);
        $data['province'] = $this->provinceModel->find($llg['province_id']);
        $data['countries'] = $this->countryModel->orderBy('name', 'ASC')->findAll();
        $data['provinces'] = $this->provinceModel->orderBy('name', 'ASC')->findAll();
        $data['districts'] = $this->districtModel->orderBy('name', 'ASC')->findAll();

        return view('dakoii/llgs_edit', $data);
    }

    /**
     * Update LLG
     */
    public function updateLlg($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $llg = $this->llgModel->find($id);
        if (!$llg) {
            session()->setFlashdata('error', 'LLG not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        $rules = [
            'name' => 'required|min_length[2]|max_length[255]',
            'llgcode' => "required|min_length[2]|max_length[20]|is_unique[adx_llg.llgcode,id,{$id}]",
            'country_id' => 'required|integer',
            'province_id' => 'required|integer',
            'district_id' => 'required|integer'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'name' => $this->request->getPost('name'),
            'llgcode' => strtoupper($this->request->getPost('llgcode')),
            'country_id' => $this->request->getPost('country_id'),
            'province_id' => $this->request->getPost('province_id'),
            'district_id' => $this->request->getPost('district_id'),
            'updated_by' => $this->session->get('dakoii_user_id')
        ];

        if ($this->llgModel->update($id, $data)) {
            session()->setFlashdata('success', 'LLG updated successfully');
            $districtId = $this->request->getPost('district_id');
            return redirect()->to("dakoii/locations/llgs/{$districtId}");
        } else {
            session()->setFlashdata('error', 'Failed to update LLG');
            return redirect()->back()->withInput();
        }
    }

    /**
     * Delete LLG
     */
    public function deleteLlg($id)
    {
        if (!$this->isAuthenticated()) {
            session()->setFlashdata('error', 'Please login to access the Dakoii portal.');
            return redirect()->to('dakoii/login');
        }

        $llg = $this->llgModel->find($id);
        if (!$llg) {
            session()->setFlashdata('error', 'LLG not found');
            return redirect()->to('dakoii/locations/provinces');
        }

        // Check if LLG has wards
        $wardCount = $this->wardModel->where('llg_id', $id)->countAllResults();
        if ($wardCount > 0) {
            session()->setFlashdata('error', 'Cannot delete LLG that has wards');
            $districtId = $llg['district_id'];
            return redirect()->to("dakoii/locations/llgs/{$districtId}");
        }

        $districtId = $llg['district_id'];
        if ($this->llgModel->delete($id)) {
            session()->setFlashdata('success', 'LLG deleted successfully');
        } else {
            session()->setFlashdata('error', 'Failed to delete LLG');
        }

        return redirect()->to("dakoii/locations/llgs/{$districtId}");
    }
}
