<?= $this->extend("templates/dakoiiadmin"); ?>
<?= $this->section('content'); ?>

<div class="container-fluid p-2">
    <div class="card">
        <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
            <div>
                <h5 class="mb-0">
                    <i class="fas fa-sitemap"></i> Wards of <?= esc($llg['name']) ?>
                </h5>
                <small class="text-white-50">
                    Province: <?= esc($province['name']) ?> | 
                    District: <?= esc($district['name']) ?> |
                    LLG Code: <?= esc($llg['llgcode']) ?>
                </small>
            </div>
            <div>
                <a href="<?= base_url('dakoii/locations/llgs/' . $district['id']) ?>" class="btn btn-light mr-2">
                    <i class="fas fa-arrow-left"></i> Back to LLGs
                </a>
                <a href="<?= base_url('dakoii/locations/wards/create/' . $llg['id']) ?>" class="btn btn-success">
                    <i class="fas fa-plus"></i> Add Ward
                </a>
            </div>
        </div>
        <div class="card-body">
            <table class="table table-hover">
                <thead class="thead-dark">
                    <tr>
                        <th>#</th>
                        <th>Ward Code</th>
                        <th>Name</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $i = 1; foreach ($wards as $ward): ?>
                    <tr>
                        <td><?= $i++ ?></td>
                        <td><?= esc($ward['wardcode']) ?></td>
                        <td><?= esc($ward['name']) ?></td>
                        <td>
                            <a href="<?= base_url('dakoii/locations/wards/edit/' . $ward['id']) ?>"
                               class="btn btn-sm btn-primary">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="<?= base_url('dakoii/locations/wards/delete/' . $ward['id']) ?>"
                               class="btn btn-sm btn-danger"
                               onclick="return confirm('Are you sure you want to delete the ward <?= esc($ward['name']) ?> from <?= esc($llg['name']) ?> LLG? This action cannot be undone.')">
                                <i class="fas fa-trash"></i>
                            </a>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Ward Modal -->
<div class="modal fade" id="addWardModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">Add New Ward</h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <?= form_open('dakoii/locations/wards/store') ?>
            <div class="modal-body">
                <input type="hidden" name="country_id" value="<?= $province['country_id'] ?>">
                <input type="hidden" name="province_id" value="<?= $province['id'] ?>">
                <input type="hidden" name="district_id" value="<?= $district['id'] ?>">
                <input type="hidden" name="llg_id" value="<?= $llg['id'] ?>">
                
                <div class="form-group">
                    <label>Ward Code</label>
                    <input type="text" class="form-control" name="wardcode" required>
                </div>
                
                <div class="form-group">
                    <label>Ward Name</label>
                    <input type="text" class="form-control" name="name" required>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-success">Save Ward</button>
            </div>
            <?= form_close() ?>
        </div>
    </div>
</div>



<?= $this->endSection() ?> 