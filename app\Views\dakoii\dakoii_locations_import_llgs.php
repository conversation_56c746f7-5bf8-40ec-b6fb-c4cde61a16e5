<?= $this->extend("templates/dakoii_template"); ?>
<?= $this->section('content'); ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/dashboard') ?>">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/locations') ?>">Locations</a></li>
        <li class="breadcrumb-item"><a href="<?= base_url('dakoii/locations/provinces') ?>">Provinces</a></li>
        <li class="breadcrumb-item active">Import LLGs CSV</li>
    </ol>
</nav>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-0">Import LLGs from CSV</h2>
        <p class="text-muted mb-0">Upload a CSV file to import multiple Local Level Governments at once</p>
    </div>
    <div class="btn-group">
        <a href="<?= base_url('dakoii/locations/llgs/sample-csv') ?>" class="btn btn-outline-info">
            <i class="fas fa-download"></i> Download Sample CSV
        </a>
        <a href="<?= base_url('dakoii/locations/provinces') ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Provinces
        </a>
    </div>
</div>

<!-- Flash Messages -->
<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle"></i> 
        <pre style="white-space: pre-wrap; margin: 0;"><?= session()->getFlashdata('error') ?></pre>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle"></i> 
        <pre style="white-space: pre-wrap; margin: 0;"><?= session()->getFlashdata('success') ?></pre>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row">
    <!-- Import Form -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-upload"></i> Upload CSV File
                </h5>
            </div>
            <div class="card-body">
                <?= form_open_multipart('dakoii/locations/llgs/import') ?>
                <?= csrf_field() ?>

                <div class="mb-4">
                    <label class="form-label">CSV File <span class="text-danger">*</span></label>
                    <input type="file" class="form-control" name="csv_file" accept=".csv" required>
                    <div class="form-text">
                        Select a CSV file containing LLG data. Maximum file size: 2MB
                    </div>
                </div>

                <div class="d-flex justify-content-between">
                    <a href="<?= base_url('dakoii/locations/provinces') ?>" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload"></i> Import LLGs
                    </button>
                </div>

                <?= form_close() ?>
            </div>
        </div>

        <!-- Available Districts -->
        <?php if (!empty($districts)): ?>
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-list"></i> Available Districts
                </h6>
            </div>
            <div class="card-body" style="max-height: 300px; overflow-y: auto;">
                <div class="row">
                    <?php foreach ($districts as $district): ?>
                    <div class="col-md-6 mb-2">
                        <span class="badge bg-light text-dark">
                            <?= esc($district['districtcode']) ?> - <?= esc($district['name']) ?>
                            <small class="text-muted">(<?= esc($district['province_name']) ?>)</small>
                        </span>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Instructions Panel -->
    <div class="col-lg-4">
        <!-- CSV Format Instructions -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle"></i> CSV Format Requirements
                </h6>
            </div>
            <div class="card-body">
                <h6>Required Columns:</h6>
                <ul class="list-unstyled">
                    <li><strong>llgcode</strong> - LLG code (unique)</li>
                    <li><strong>name</strong> - LLG name</li>
                    <li><strong>districtcode</strong> - District code (must exist)</li>
                </ul>

                <h6 class="mt-3">Example:</h6>
                <div class="bg-light p-2 rounded">
                    <code>
                        llgcode,name,districtcode<br>
                        140101,"Ambunti Urban LLG","1401"<br>
                        140102,"Drekikier LLG","1401"<br>
                        140201,"Angoram Urban LLG","1402"
                    </code>
                </div>

                <div class="mt-3">
                    <a href="<?= base_url('dakoii/locations/llgs/sample-csv') ?>" class="btn btn-sm btn-outline-info">
                        <i class="fas fa-download"></i> Download Sample
                    </a>
                </div>
            </div>
        </div>

        <!-- Import Notes -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-exclamation-triangle"></i> Important Notes
                </h6>
            </div>
            <div class="card-body">
                <ul class="small">
                    <li>LLG codes must be unique</li>
                    <li>District codes must exist in the system</li>
                    <li>Duplicate codes will be skipped</li>
                    <li>Empty rows will be ignored</li>
                    <li>Maximum file size is 2MB</li>
                    <li>Only CSV files are accepted</li>
                </ul>

                <div class="alert alert-warning mt-3">
                    <i class="fas fa-info-circle"></i>
                    <strong>Tip:</strong> Make sure all districts are created before importing LLGs.
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
