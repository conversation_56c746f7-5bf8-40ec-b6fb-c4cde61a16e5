<?php

namespace App\Controllers;

use App\Models\AdxDistrictModel;
use App\Models\DakoiiOrgModel;
use App\Models\PermissionsUserDistrictsModel;
use App\Models\AdxProvinceModel;
use App\Models\UsersModel;

class Home extends BaseController
{
    public $session;
    public $UsersModel;
    public $orgModel;
    public $provinceModel;
    public $districtModel;
    public $districtPermissionModel;



    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();

        $this->UsersModel = new UsersModel();
        $this->orgModel = new DakoiiOrgModel();
        $this->provinceModel = new AdxProvinceModel();
        $this->districtModel = new AdxDistrictModel();
        $this->districtPermissionModel = new PermissionsUserDistrictsModel();
    }

    public function index()
    {
        $data['title'] = "Home";
        $data['menu'] = "home";

        echo view('home/home', $data);
    }

    public function about()
    {
        $data['title'] = "About Org.Calendar";
        $data['menu'] = "about";
        echo view('home/about', $data);
    }


   

    /**
     * Display login form (GET request)
     */
    public function login()
    {
        // Display login form with updated label for Email/ID
        $data['title'] = "Login";
        $data['menu'] = "login";
        // Use the home_login view
        return view('home/home_login', $data);
    }

    /**
     * Process login form submission (POST request)
     */
    public function processLogin()
    {
        // Validate form data
        $rules = [
            'identifier' => 'required',
            'password' => 'required'
        ];

        if (!$this->validate($rules)) {
            return redirect()->to('login')->with('error', 'Please enter both identifier and password');
        }

        // Retrieve form data
        $identifier = $this->request->getVar('identifier');
        $password = $this->request->getVar('password');

        // Authenticate user using UsersModel
        $user = $this->authenticateUser($identifier, $password);

        if (!$user) {
            return redirect()->to('login')->with('error', 'Incorrect Email/ID or Password!');
        }

        // Check if user is associated with an organization
        if (empty($user['org_id'])) {
            return redirect()->to('login')->with('error', 'Username is not associated with any organization!');
        }

        // Get organization information
        $org = $this->orgModel->where('id', $user['org_id'])->first();

        // Check if organization is active
        if (!$org || $org['is_active'] != 1) {
            return redirect()->to('login')->with('error', 'Your Organization has been Deactivated!');
        }

        // Check if user account is active
        if ($user['status'] != 1) {
            return redirect()->to('login')->with('error', 'Your account is not active!');
        }

        // Set up user session
        $this->setupUserSession($user, $org);

        // Redirect based on user role
        return $this->redirectUserByRole($user['role']);
    }

    /**
     * Authenticate user by email or ID
     */
    private function authenticateUser($identifier, $password)
    {
        // Check if user exists by email or ID
        $user = $this->UsersModel->where('email', $identifier)
                                 ->orWhere('id', $identifier)
                                 ->first();

        // If user not found, return false
        if (!$user) {
            return false;
        }

        // Check if password is correct
        if (!password_verify($password, $user['password'])) {
            return false;
        }

        return $user;
    }

    /**
     * Set up user session data
     */
    private function setupUserSession($user, $org)
    {
        // Get default district
        $districtPermission = $this->districtPermissionModel
            ->where('user_id', $user['id'])
            ->where('org_id', $user['org_id'])
            ->where('default_district', 1)
            ->first();

        // Get all assigned districts
        $allDistrictPermissions = $this->districtPermissionModel
            ->where('user_id', $user['id'])
            ->where('org_id', $user['org_id'])
            ->findAll();
        $assignedDistricts = [];

        foreach($allDistrictPermissions as $permission) {
            $district = $this->districtModel->where('id', $permission['district_id'])->first();
            if($district) {
                $assignedDistricts[] = [
                    'id' => $district['id'],
                    'name' => $district['name'],
                    'is_default' => $permission['default_district'] == 1
                ];
            }
        }

        // Set session data for assigned districts
        $this->session->set('assigned_districts', $assignedDistricts);

        // Set default district if available
        $this->setDefaultDistrict($districtPermission, $assignedDistricts);

        // Store user data in session
        $this->session->set('emp_id', $user['id']);
        $this->session->set('email', $user['email']);
        $this->session->set('phone', $user['phone']);
        $this->session->set('name', $user['name']);
        $this->session->set('role', $user['role']);
        $this->session->set('position', $user['position']);
        $this->session->set('status', $user['status']);
        $this->session->set('orgname', $org['name']);
        $this->session->set('orglogo', $org['orglogo']);
        $this->session->set('orgcode', $org['orgcode']);
        $this->session->set('org_id', $org['id']);
        $this->session->set('orgcountry_id', $org['addlockcountry']);
        $this->session->set('orgprovince_id', $org['addlockprov']);
        $this->session->set('logged_in', true);

        // Get province information
        $province = $this->provinceModel->where('id', $org['addlockprov'])->first();
        if ($province) {
            $this->session->set('province_json_id', $province['json_id']);
        }
    }

    /**
     * Set default district in session
     */
    private function setDefaultDistrict($districtPermission, $assignedDistricts)
    {
        if($districtPermission){
            $district = $this->districtModel->where('id', $districtPermission['district_id'])->first();
            if($district) {
                $this->session->set('district_id', $district['id']);
                $this->session->set('district_name', $district['name']);
            } else {
                // If default district not found, try to use the first available district
                if(!empty($assignedDistricts)) {
                    $this->session->set('district_id', $assignedDistricts[0]['id']);
                    $this->session->set('district_name', $assignedDistricts[0]['name']);
                } else {
                    // No districts assigned
                    $this->session->set('district_id', null);
                    $this->session->set('district_name', 'No District Assigned');
                }
            }
        } else {
            // No default district set, try to use the first available district
            if(!empty($assignedDistricts)) {
                $this->session->set('district_id', $assignedDistricts[0]['id']);
                $this->session->set('district_name', $assignedDistricts[0]['name']);
            } else {
                // No districts assigned
                $this->session->set('district_id', null);
                $this->session->set('district_name', 'No District Assigned');
            }
        }
    }

    /**
     * Redirect user based on their role
     */
    private function redirectUserByRole($role)
    {
        if ($role == 'admin' || $role == 'supervisor' || $role == 'guest') {
            // Redirect to admin dashboard
            return redirect()->to('dashboard');
        } else {
            // Redirect to staff dashboard
            return redirect()->to('staff');
        }
    }

    public function logout()
    {
        // Destroy the user's session
        $session = session();
        $session->destroy();

        // Redirect to the login page
        return redirect()->to(base_url());
    }
}
